<?php

namespace App\Services;
use DB;
use Illuminate\Support\Facades\Log;

class EsimGoService
{
    

    public function __construct()
    {
        //
    }

    public function add($bundleName){

        #region BUILD POSTFIELD ( DATA-RAW )
        $post_field = array(
            'type' => 'transaction',
            'assign' => false,
            'Order' => array(
                array(
                'type' => 'bundle',
                'quantity' => 1,
                'item' => $bundleName
                )
            )
        );
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/orders');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, 1 );
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_field) ); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return $res_esimgo;
        #endregion
    }

    public function apply($bundleName, $iccID){

        #region BUILD POSTFIELD ( DATA-RAW )
        $arrayBundle = array(
            $bundleName
        );
        $post_field = array(
            'iccid' => $iccID,
            'name' => $bundleName
        );

        if(empty($iccID)){
            $post_field = array(
                'name' => $bundleName
            );
        }
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/apply');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, 1 );
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_field) ); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return $res_esimgo;
        #endregion
    }

    public function applyNoEsim($bundleName){

        #region BUILD POSTFIELD ( DATA-RAW )
        $post_field = array(
            'name' => $bundleName,
        );
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/apply');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, 1 );
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_field) ); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return $res_esimgo;
        #endregion
    }

    public function revoke($bundleName, $iccID){

        #region BUILD POSTFIELD ( DATA-RAW )
        $post_field = array(
            'name' => $bundleName,
        );
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID . '/bundles/' . $bundleName . "?refundToBalance=true" );
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "DELETE");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return $res_esimgo;
        #endregion
    }

    public function reloadValidateBeforeCheckout($bundleName, $iccID){

        #region BUILD POSTFIELD ( DATA-RAW )
        $post_field = array(
            'type' => 'validate',
            'assign' => true,
            'Order' => array(
                array(
                'type' => 'bundle',
                'quantity' => 1,
                'item' => $bundleName,
                'iccids' => array(
                    $iccID
                )
                )
            )
        );
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/orders');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, 1 );
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_field) ); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return $res_esimgo;
        #endregion
    }

    public function addToInventory($bundleName, $iccID){

        #region BUILD POSTFIELD ( DATA-RAW )
        $post_field = array(
            'type' => 'transaction',
            'assign' => false,
            'Order' => array(
                array(
                'type' => 'bundle',
                'quantity' => 1,
                'item' => $bundleName
                )
            )
        );
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/orders');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, 1 );
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_field) ); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return $res_esimgo;
        #endregion
    }

    // Retourne si c'est encore un bundle valid avant l'achat
    public function validation($bundleName){

        #region BUILD POSTFIELD ( DATA-RAW )
        $post_field = array(
            'type' => 'validate',
            'assign' => false,
            'Order' => array(
                array(
                'type' => 'bundle',
                'quantity' => 1,
                'item' => $bundleName
                )
            )
        );
        #endregion

        #region cURL ESIM-GO
        $apiKey = getApiKey_eSimGo();

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/orders');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey ,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POST, 1 );
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post_field) ); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        return json_decode($res_esimgo)->valid;
        #endregion
    }

    public function getBundlesInformations($iccID){

        $apiKey = getApiKey_eSimGo();
         #region cURL ESIM-GO
         $ch = curl_init();
         curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID . '/bundles');
         curl_setopt($ch, CURLOPT_HTTPHEADER, [
             'X-API-Key: ' . $apiKey
         ]);
         curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
         curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
         curl_setopt($ch, CURLOPT_HEADER, 0);
         curl_setopt($ch, CURLOPT_NOBODY, 0);
         curl_setopt($ch, CURLOPT_TIMEOUT, 30);
         $res_esimgo = curl_exec($ch);
         curl_close($ch);

         #endregion

         return $res_esimgo;
        
    }

    public function getEsimInfo($orderReference){

        $apiKey = getApiKey_eSimGo();
        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/assignments/' . $orderReference);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        #endregion

        #region Traitement du résultat
        $res_esimgo = explode("\n", $res_esimgo);
        if(isset($res_esimgo[0])){
            $res_esimgo = explode(",", $res_esimgo[0]);
            $res_esimgo = (object) [
                'iccID' => $res_esimgo[0],
                'matchingID' => $res_esimgo[0],
                'rsp' => $res_esimgo[0],
                'currentBundle' => $res_esimgo[0],
                'reference' => $res_esimgo[0],
            ];
        }else{
            Log::info($res_esimgo);
            $res_esimgo = (object) [
                'iccID' => "",
                'matchingID' => "",
                'rsp' => "",
                'currentBundle' => "",
                'reference' => "",
            ];
            
        }
        
        #endregion

        return $res_esimgo;

    }

    public function getEsimInfoFromEsim($esim){

        $apiKey = getApiKey_eSimGo();
        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $esim);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        #endregion

        #region Traitement du résultat
        //$res_esimgo = explode("\n", $res_esimgo);
        /*if(isset($res_esimgo[0])){
            $res_esimgo = explode(",", $res_esimgo[0]);
            $res_esimgo = (object) [
                'iccID' => $res_esimgo[0],
                'matchingID' => $res_esimgo[0],
                'rsp' => $res_esimgo[0],
                'currentBundle' => $res_esimgo[0],
                'reference' => $res_esimgo[0],
            ];
        }else{
            Log::info($res_esimgo);
            $res_esimgo = (object) [
                'iccID' => "",
                'matchingID' => "",
                'rsp' => "",
                'currentBundle' => "",
                'reference' => "",
            ];
            
        }*/
        
        #endregion

        return $res_esimgo;

    }

    public function getInstallStatus($iccID){
            $apiKey = getApiKey_eSimGo();
            #region cURL ESIM-GO
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'X-API-Key: ' . $apiKey
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_NOBODY, 0);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            $res_esimgo = curl_exec($ch);
            curl_close($ch);
    
            #endregion
    
            return $res_esimgo;
    
    }

    // Retrieve a bundle info ( only one )
    public function getBundleInfo($bundle_name){
        $bundleTable = getUpdatedBundleTable();
        $bundle = DB::select("SELECT * FROM " . $bundleTable . " WHERE name = ?", [$bundle_name]);
        if(count($bundle) > 0){
            return $bundle[0];
        }else{
            return null;
        }
    }

    public function getQrCode($reference){
        $apiKey = getApiKey_eSimGo();
        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/assignments/?reference=' . $reference);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey,
            'Accept: application/zip'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        #endregion

        return $res_esimgo;

    }

    public function sendConfirmationSMS($iccID){

        $apiKey = getApiKey_eSimGo();
        $data = array(
            'message' => "Bienvenue chez Simeo! \nVotre eSIM est activée et opérationnelle immédiatement.",
            'from' => 'eSIM'
        );

        $query_string = http_build_query($data, '', null, PHP_QUERY_RFC3986);
        $query_string = str_replace('%20', ' ', $query_string);
        $query_string = "message=" . $data['message'] . "&from=" . $data['from'];

        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/esims/' . $iccID . '/sms');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $query_string ); 
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        #endregion

        return $res_esimgo;

    }

    public function sendAlertSMS($iccID, $message)
    {
        try {
            $apiKey = getApiKey_eSimGo();
            
            if (empty($iccID) || empty($message)) {
                Log::error('EsimGoService: Missing required parameters for SMS', [
                    'iccID' => $iccID,
                    'message' => $message
                ]);
                return false;
            }

            // Calculer la longueur maximale disponible pour le message
            $maxMessageLength = 160 - strlen($iccID) - 3; // 3 caractères pour les espaces et tirets

            // Tronquer le message si nécessaire
            if (strlen($message) > $maxMessageLength) {
                $originalMessage = $message;
                $message = substr($message, 0, $maxMessageLength - 3) . '...';
                Log::info('EsimGoService: Message truncated to fit SMS limit', [
                    'original_length' => strlen($originalMessage),
                    'truncated_length' => strlen($message),
                    'iccID_length' => strlen($iccID),
                    'original_message' => $originalMessage,
                    'truncated_message' => $message
                ]);
            }

            $data = [
                'message' => $message,
                'from' => 'eSIM'
            ];

            // Construire manuellement la query string pour éviter l'encodage des espaces en %20
            $query_string = 'message=' . $data['message'] . '&from=' . $data['from'];

            $ch = curl_init();
            curl_setopt_array($ch, [
                CURLOPT_URL => 'https://api.esim-go.com/v2.5/esims/' . urlencode($iccID) . '/sms',
                CURLOPT_HTTPHEADER => [
                    'X-API-Key: ' . $apiKey
                ],
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST => true,
                CURLOPT_POSTFIELDS => $query_string,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_SSL_VERIFYPEER => true
            ]);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                Log::error('EsimGoService: SMS sending failed', [
                    'error' => curl_error($ch),
                    'iccID' => $iccID
                ]);
                curl_close($ch);
                return false;
            }
            
            curl_close($ch);

            if ($httpCode >= 200 && $httpCode < 300) {
                Log::info('EsimGoService: SMS sent successfully', [
                    'iccID' => $iccID,
                    'response' => $response
                ]);
                return true;
            }

            Log::error('EsimGoService: SMS sending failed with status code', [
                'httpCode' => $httpCode,
                'response' => $response,
                'iccID' => $iccID,
                'message' => $message
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error('EsimGoService: Exception while sending SMS', [
                'error' => $e->getMessage(),
                'iccID' => $iccID
            ]);
            return false;
        }
    }

    public function getNetworksByCountry($country){

        $apiKey = getApiKey_eSimGo();
        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/networks?isos=' . $country);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: ' . $apiKey
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        #endregion

        return $res_esimgo;

    }

}
