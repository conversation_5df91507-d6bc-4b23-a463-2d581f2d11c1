<?php

namespace App\Http\Controllers;
use DB;
use Illuminate\Http\Request;
use App\Services\EsimGoService;
use App\Http\Controllers\eSimController;
use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;

class PublicController extends BaseController
{
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(EsimGoService $eSimGoService, BundleController $bundleController, eSimController $eSimController)
    {
        $this->eSimGoService = $eSimGoService;
        $this->bundleController = $bundleController;
        $this->eSimController = $eSimController;
        $this->middleware('auth:api', ['except' => ['bundlesByCountry', 'bundlesByCountryUnlimited', 'bundlesByCountryGo', 'getNetworkByCountry', 'countries', 'bundlesByRegion', 'getDiscounts']]);
    }


    /* Deprecated */
    public function bundlesByCountry(Request $request)
    {

        $this->validate($request, [
            'country' => 'required|string',
        ]);

        $countryIso = $request->input('country');

        $bundleTable = getUpdatedBundleTable();

        if(trim($countryIso) == 'Europe'){
            $countryIso = 'Europe+';
        }

        $bundles = DB::select("SELECT * FROM " . $bundleTable . " WHERE main_country = '" . $countryIso . "'");

        return $bundles;

    }

    public function bundlesByCountryGo(Request $request)
    {

        $this->validate($request, [
            'country' => 'required|string',
        ]);

        $countryIso = $request->input('country');

        $bundleTable = getUpdatedBundleTable();

        if(trim($countryIso) == 'Europe'){
            $countryIso = 'Europe+';
        }

        // Ajouter le prix avec rabais si disponible + supprimer le prix du distributeu
        $bundles = DB::select("SELECT * FROM " . $bundleTable . " WHERE main_country = '" . $countryIso . "' AND isUnlimited = 0 ORDER BY dataAmount");
        $foundBestSeller = false;
        foreach($bundles as $key => $bundle){
            unset($bundles[$key]->distributorPrice);
            $bundles[$key] = $this->bundleController->getBundlePriceWithDiscount($bundle);
            if($bundles[$key]->price > 33 && !$foundBestSeller){
                $bundles[$key]->isBestSeller = true;
                $foundBestSeller = true;
            }else{
                $bundles[$key]->isBestSeller = false;
            }
        }

        if(!$foundBestSeller){
            $bundles[count($bundles) - 1]->isBestSeller = true;
        }

        return $bundles;

    }

    public function bundlesByRegion(Request $request)
    {
        $this->validate($request, [
            'country' => 'required|string',
        ]);

        $countryIso = $request->input('country');

        $countryTable = getUpdatedCountryTable();

        $region = DB::select("SELECT * FROM " . $countryTable . " WHERE iso = '" . $countryIso . "'");

        if(count($region) == 0 || !isset($region[0])){
            return [];
        }

        $bundleTable = getUpdatedBundleTable();
        $region = $region[0]->region;
        $bundleIso = '';

        if($region == 'Europe'){
            $bundleIso = 'Europe+';
        }else if($region == 'Asia'){
            $bundleIso = 'Asia';
        }else if($region == 'South America'){
            $bundleIso = 'LATAM';
        }else if($region == 'Africa'){
            $bundleIso = 'Africa';
        }else if($region == 'North America'){
            $bundleIso = 'North America';
        }else if($region == 'Middle East'){
            $bundleIso = 'Middle East';
        }else if($region == 'Oceania'){
            $bundleIso = 'Oceania';
        }else if($region == 'Caribbean'){
            $bundleIso = 'Caribbean';
        }else if($region == 'Global'){
            $bundleIso = 'Global';
        }

        $bundles = DB::select("SELECT * FROM " . $bundleTable . " WHERE main_country = '" . $bundleIso . "' AND isUnlimited = 0 ORDER BY dataAmount");
        $countries = $bundles[0]->countries;
        $countries = json_decode($countries);

        // Ajouter le prix avec rabais si disponible + supprimer le prix du distributeur
        $foundBestSeller = false;
        foreach($bundles as $key => $bundle){
            unset($bundles[$key]->distributorPrice);
            $bundles[$key] = $this->bundleController->getBundlePriceWithDiscount($bundle);
            if($bundles[$key]->price > 33 && !$foundBestSeller){
                $bundles[$key]->isBestSeller = true;
                $foundBestSeller = true;
            }else{
                $bundles[$key]->isBestSeller = false;
            }
        }
        
        if(in_array($countryIso, $countries) || $bundleIso == 'Caribbean' || $bundleIso == 'Global'){
            return $bundles;
        }else{
            return [];
        }

        return $bundles;


    }

    public function bundlesByCountryUnlimited(Request $request)
    {

        $this->validate($request, [
            'country' => 'required|string',
        ]);

        $countryIso = $request->input('country');

        $bundleTable = getUpdatedBundleTable();

        if(trim($countryIso) == 'Europe'){
            $countryIso = 'Europe+';
        }

        $bundles = DB::select("SELECT * FROM " . $bundleTable . " WHERE main_country = '" . $countryIso . "' AND isUnlimited = 1 ORDER BY duration");

        return $bundles;

    }

    public function getNetworkByCountry(Request $request)
    {

        $this->validate($request, [
            'country' => 'required|string',
            'distributorID' => 'required|integer'
        ]);

        $countryIso = $request->input('country');
        $distributorID = $request->input('distributorID');

        $networks = [];
        if($distributorID == 1){
            $networks = $this->eSimGoService->getNetworksByCountry($countryIso);
        }

        

        return $networks;

    }

    public function countries()
    {
        $countryTable = getUpdatedCountryTable();
        $bundleTable = getUpdatedBundleTable();
        $countries = DB::select("SELECT c.*, MIN(b.price) AS minprice FROM " . $countryTable . " c LEFT JOIN " . $bundleTable . " b ON c.iso = b.main_country WHERE c.type = 'C' GROUP BY c.iso, c.id, c.name, c.region, c.type ORDER BY c.name");


        foreach($countries as $key => $country){
            $getDiscountInfo = $this->bundleController->getBundleMinPrice($country->iso);

            $countries[$key]->minprice = $getDiscountInfo[0];
            $countries[$key]->is_discount = $getDiscountInfo[1];
        }

        return $countries;
    }

    public function getDiscounts(){
        $discounts = DB::select("SELECT * FROM discounts WHERE is_active = 1 AND percentage > 9 ORDER BY percentage DESC");
        $discountsArray = [];
        $countriesTable = getUpdatedCountryTable();
        foreach($discounts as $key => $discount){
            $bundleName = $discount->bundleName;
            $bundleName = explode("_", $bundleName);
            $country = $this->eSimController->validateZone($bundleName[3]);
            $countryObject = DB::select("SELECT * FROM " . $countriesTable . " WHERE iso = '" . $country . "'");
            if(!isset($discountsArray[$country])){
                $discountsArray[$country] = $discount;
                
                if($countryObject[0]->iso == 'Caribbean'){
                    $countryObject[0]->iso2 = 'BS';
                    $countryObject[0]->countries = ["AG", "AI", "AN", "BB", "BM", "BQ", "BS", "CW", "DM", "GD", "GF", "GY", "HT", "JM", "KN", "KY", "LC", "MQ", "MS", "SR", "SV", "TC", "TT", "VC", "VG"];
                }
                $discountsArray[$country]->country = $countryObject[0];
            }else{
                $currentDiscount = $discountsArray[$country];
                if($discount->percentage > $currentDiscount->percentage){
                    $discountsArray[$country] = $discount;
                    if($countryObject[0]->iso == 'Caribbean'){
                        $countryObject[0]->iso2 = 'BS';
                        $countryObject[0]->countries = ["AG", "AI", "AN", "BB", "BM", "BQ", "BS", "CW", "DM", "GD", "GF", "GY", "HT", "JM", "KN", "KY", "LC", "MQ", "MS", "SR", "SV", "TC", "TT", "VC", "VG"];
                    }
                    $discountsArray[$country]->country = $countryObject[0];
                }
            }
        }

        // object to array
        $discountsArray = array_values($discountsArray);
        usort($discountsArray, function($a, $b) {
            return $b->percentage - $a->percentage;
        });

        return ['name' => "Promotions", 'discounts' => $discountsArray];
    }

}
