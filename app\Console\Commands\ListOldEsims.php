<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use DB;

class ListOldEsims extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:listOldEsims {--months=2 : Nombre de mois d\'ancienneté}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Liste les cartes eSIM dont le last_apply_date est antérieur au nombre de mois spécifié (défaut : 2 mois).';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Récupère le nombre de mois depuis l'option, défaut 2
        $months = intval($this->option('months'));
        if ($months <= 0) {
            $months = 2;
        }

        // Calcule la date seuil au format Ymd
        $threshold = date('Ymd', strtotime('-' . $months . ' months'));

        $this->info('Liste des eSIM dont last_apply_date < ' . $threshold . ' (-' . $months . ' mois)');

        // Sélectionne les eSIMs correspondantes
        $esims = DB::select('SELECT * FROM esims WHERE last_apply_date < ? ORDER BY last_apply_date ASC', [$threshold]);

        if (count($esims) === 0) {
            $this->info('Aucune eSIM trouvée.');
            return 0;
        }


        $this->info('Insertion des eSIMs obsolètes dans la table esims_deleted…');
        // Copie les enregistrements dans la table d\'archive tout en évitant les doublons
        DB::insert('INSERT IGNORE INTO esims_deleted SELECT * FROM esims WHERE last_apply_date < ?', [$threshold]);

        foreach ($esims as $esim) {
            $this->line('Archivée → ICCID : ' . $esim->iccid . ' | user_id : ' . $esim->user_id . ' | last_apply_date : ' . $esim->last_apply_date);
        }

        // Suppression des eSIM archivées de la table principale
        DB::delete('DELETE FROM esims WHERE last_apply_date < ?', [$threshold]);

        $this->info(count($esims) . ' eSIM(s) archivées puis supprimées de la table esims.');

        return 0;
    }
} 