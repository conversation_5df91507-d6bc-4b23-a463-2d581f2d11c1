<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

use DB;

class SyncCountries extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:syncCountries';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync countries from apis';


    public $countryArray;
    public $countryIsoArray;
    public $numberCountry;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->countryArray = array();
        $this->countryIsoArray = array();
        $this->numberCountry = 1;

        $this->syncEsimGo();

        //$this->syncDataPlans();

        $this->SyncDatabase();
    }

    public function SyncDatabase(){
        #region SYNC DATABASE

        // Get Options
        $outdatedCountriesTable = DB::select("SELECT meta_value FROM options WHERE meta_key='outdated_countries_table'")[0]->meta_value;
        $updatedCountriesTable = DB::select("SELECT meta_value FROM options WHERE meta_key='updated_countries_table'")[0]->meta_value;

        // Execute SYNC
        $sorted = $this->array_orderby($this->countryArray, 'name', SORT_ASC);
        
        $truncate = DB::select("TRUNCATE TABLE " . $outdatedCountriesTable);
        foreach($sorted as $country){
            $results = DB::select("INSERT INTO " . $outdatedCountriesTable . " (id, name, region, iso, type) VALUES('" . $country['id'] . "' ,'" . $country['name'] . "', '" . $country['region'] . "', '" . $country['iso'] . "', '" . $country['type'] . "') ON DUPLICATE KEY UPDATE    
            name='" . $country['name'] . "', region='" . $country['region'] . "', iso='" . $country['iso'] . "', type='" . $country['type'] . "'");
        }

        // Update Options
        DB::select("UPDATE options SET meta_value = '" . date("Y-m-d H:i:s") . "' WHERE meta_key = '" . $outdatedCountriesTable . "_last_sync'");
        DB::select("UPDATE options SET meta_value = '" . $updatedCountriesTable . "' WHERE meta_key = 'outdated_countries_table'");
        DB::select("UPDATE options SET meta_value = '" . $outdatedCountriesTable . "' WHERE meta_key = 'updated_countries_table'");

        #endregion
    }

    public function syncEsimGo(){
        #region cURL ESIM-GO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://api.esim-go.com/v2.3/catalogue?perPage=100000&group=Standard%20Bundles%20April%202024');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'X-API-Key: jdSohhA-m52upbK_pxL0Sa8yYIaSWAz6CaTjvG3X'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_esimgo = curl_exec($ch);
        curl_close($ch);

        $results_obj_esimgo = json_decode($res_esimgo);
        #endregion

        #region ESIM-GO SYNC RESULT
        foreach($results_obj_esimgo->bundles as $bundle){

            foreach($bundle->countries as $country){
                $nameCountry = $country->name;
                $isoCountry = $country->iso;
                
                $isCountryInDatabase = in_array($isoCountry,$this->countryIsoArray);
                if(!$isCountryInDatabase){
                    if(!in_array($country->name, array('Europe+', 'Global', 'LATAM', 'Asia', 'Africa', 'Middle East', 'Oceania', 'North America', 'Caribbean'))){

                        // SI PAYS SONT DANS LA LISTE DES CARAIBES, ON CHANGE LA REGION
                        $caraibes = ["AG","AI","AN","BB","BM","BQ","BS","CW","DM","GD","GF","GY","HT","JM","KN","KY","LC","MQ","MS","SR","SV","TC","TT","VC","VG"];
                        if(in_array($isoCountry, $caraibes)){
                            $region = 'Caribbean';
                        }else{
                            $region = $country->region;
                        }

                        $this->countryArray[] = [
                            'id' => $this->numberCountry,
                            'name' => $nameCountry,
                            'region' => $region,
                            'iso' => $isoCountry,
                            'type' => 'C'
                        ];
                        $this->countryIsoArray[] = $isoCountry;
                        $this->numberCountry++;
                    }else if(!in_array($country->name, array('Global'))){
                        // SI REGION
                        $this->countryArray[] = [
                            'id' => $this->numberCountry,
                            'name' => $nameCountry,
                            'region' => $country->region,
                            'iso' => $isoCountry,
                            'type' => 'R'
                        ];
                        $this->countryIsoArray[] = $isoCountry;
                        $this->numberCountry++;
                    }else{
                        // SI MONDIALE
                        $this->countryArray[] = [
                            'id' => $this->numberCountry,
                            'name' => $nameCountry,
                            'region' => $country->region,
                            'iso' => $isoCountry,
                            'type' => 'M'
                        ];
                        $this->countryIsoArray[] = $isoCountry;
                        $this->numberCountry++;
                    }
                }
            }
        }
        #endregion
    }

    public function syncDataPlans(){
        #region cURL DATAPLANS.IO
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://app.dataplans.io/api/v1/countries');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzaWQiOiIwODJmNzYzZi0yZDQ3LTQ3NTQtYTJmNy0wYjZiYWQ2NDU0ZjYiLCJpYXQiOjE2ODE1NzAxNjMsImV4cCI6MjU0NTQ4Mzc2M30.lHeCeqcH8nCphwGMfcuj0aQ6mSVBZ_eHxzwKNQ6Fls8',
            'accept: application/json'
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_NOBODY, 0);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $res_dataplans = curl_exec($ch);
        curl_close($ch);

        $results_obj_dataplans = json_decode($res_dataplans);
        #endregion

        #region DATAPLANS.IO SYNC RESULT
        foreach($results_obj_dataplans as $country){
            $countryIso = $country->countryCode;
            $countryName = $country->countryName;

            $isCountryInDatabase = in_array($countryIso,$this->countryIsoArray);
            if(!$isCountryInDatabase){
                $this->countryArray[] = [
                    'id' => $this->numberCountry,
                    'name' => $countryName,
                    'region' => "",
                    'iso' => $countryIso,
                    'type' => 'C'
                ];
                $this->countryIsoArray[] = $countryIso;
                $this->numberCountry++;
            }
        }
        #endregion
    }

    #region HELPERS
    public function array_orderby()
    {
        $args = func_get_args();
        $data = array_shift($args);
        foreach ($args as $n => $field) {
            if (is_string($field)) {
                $tmp = array();
                foreach ($data as $key => $row)
                    $tmp[$key] = $row[$field];
                $args[$n] = $tmp;
                }
        }
        $args[] = &$data;
        call_user_func_array('array_multisort', $args);
        return array_pop($args);
    }
    #endregion
}
