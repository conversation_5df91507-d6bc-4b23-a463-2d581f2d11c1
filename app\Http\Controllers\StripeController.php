<?php

namespace App\Http\Controllers;
use DB;
use Illuminate\Http\Request;
use App\Services\EsimGoService;
use App\Http\Controllers\BundleController;
use App\Http\Controllers\eSimController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Lumen\Routing\Controller as BaseController;

class StripeController extends BaseController
{
    
    public $iccID;
    public $bundleName;
    public $startTime;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(BundleController $BundleController, eSimController $eSimController)
    {
        $this->middleware('auth:api', ['except' => ['listenerPayment', 'checkout']]);
        $this->bundleController = $BundleController;
        $this->eSimController = $eSimController;
    }

    public function checkoutCredit(Request $request){
        
        $this->validate($request, [
            'bundleName' => 'required|string',
            'distributorID' => 'required|numeric',
            'bundlePrice' => 'required|numeric'
        ]);

        $bundleName = $request->input('bundleName');
        $distributorID = $request->input('distributorID');
        $bundlePriceRequest = number_format($request->input('bundlePrice'), 2);

        $bundlePrice = $this->bundleController->getBundlePrice($bundleName);
        $bundleCreditUsed = $this->bundleController->getBundleCreditUsed($bundleName, auth()->user()->credit);

        if($bundlePriceRequest == $bundleCreditUsed){
            $bundlePriceRequest = 0;
        }
        if($bundlePrice == $bundlePriceRequest && $bundlePriceRequest == 0){
            $user_id = auth()->user()->id;

            $iccID = $this->eSimController->checkIfUserHaveEsim($user_id, $bundleName);
            if( $iccID != false ){
                $this->bundleController->addToInventory($iccID, $bundleName, $distributorID, "", $user_id, $bundleCreditUsed);
                return [
                    'status' => 'success',
                    'message' => 'reloaded'
                ];
            }else{   
                Log::info("PAS DEJA SIM - CREDIT");
                $this->bundleController->addFirstBundle($bundleName, $distributorID, "", $user_id, $bundleCreditUsed);
                return [
                    'status' => 'success',
                    'message' => 'added'
                ];
            }
        }else{
            return [
                'status' => 'error',
                'message' => 'Le prix du forfait différent du prix envoyé : ' . $bundlePrice . ' - ' . $bundlePriceRequest . ' - ' . $bundleCreditUsed
            ];
        }


    }

    public function checkout(Request $request){
        
        // Dev ( compte live )
        //$stripe = new \Stripe\StripeClient('sk_test_51N0QkII934CqzGCuvU5O4U841kU9JJcOK6sM9zjxOOkllTpPUh4yhuBZBzllh3cJhkPUelucQOzvy8lKETRslLOS00HDjsQilx');

        // Dev Staging
        //$stripe = new \Stripe\StripeClient('sk_test_51PHrmBHcGR1hqoMIeBaSrphxIv3LdyJiXnMFHkRSld6ZqMVvmcEkdaRY25BN2PkExkWCtq29WMgQ4X1anr069zsq000p9JZb4P');

        // Prod
        $stripe = new \Stripe\StripeClient('***********************************************************************************************************');

        // Use an existing Customer ID if this is a returning customer.
        if(!empty(auth()->user()->stripe_customer_id)){
            $customer = $stripe->customers->retrieve(
                auth()->user()->stripe_customer_id,
                []
            );
        }else{
            $customer = $stripe->customers->create();
        }

        $ephemeralKey = $stripe->ephemeralKeys->create([
            'customer' => $customer->id,
        ], [
            'stripe_version' => '2024-04-10',
        ]);
        $bundlePrice = $this->bundleController->getBundlePrice($_POST['bundleName']);
        //Log::info($bundlePrice);
        if($bundlePrice <= 0){

            echo json_encode([
                'paymentIntent' => null,
                'ephemeralKey' => null,
                'customer' => null,
                'bundleName' => null,
                'userID' => null,
                'distributorID' => null,
                'publishableKey' => null,
                'status' => 'free',
                'message' => 'gratuit'
            ]);
            http_response_code(200);
            return;
        }else{

            $paymentIntent = $stripe->paymentIntents->create([
                'amount' => $bundlePrice*100,
                'currency' => 'cad',
                'customer' => $customer->id,
                'automatic_payment_methods' => [
                    'enabled' => 'true',
                ],
                'metadata' => [
                    'bundleName' => $_POST['bundleName'],
                    'distributorID' => $_POST['distributorID'],
                    'userID' => auth()->user()->id
                ],
                'receipt_email' => auth()->user()->email
            ]);
        }

        // Validation du prix du bundles
        if($bundlePrice != $_POST['bundlePrice']){
            Log::info("Prix du forfait différent du prix envoyé : " . $bundlePrice . " - " . $_POST['bundlePrice']);
            echo json_encode([
                'paymentIntent' => null,
                'ephemeralKey' => null,
                'customer' => null,
                'bundleName' => null,
                'userID' => null,
                'distributorID' => null,
                'publishableKey' => null,
                'status' => 'error',
                'message' => 'Le prix du forfait différent du prix envoyé : ' . $bundlePrice . ' - ' . $_POST['bundlePrice']
            ]);
            http_response_code(200);
            
        }

        //$publishableKeyDevOld = 'pk_test_51N0QkII934CqzGCuBS7pwT3TNEZv8NfqF6PhOaJGY9yMo7W7pI5GZd07owsr81ZfMpAIfOSCJnPeorIe51ctfXaR000LXEAHvND';
        //$publishableKeyDev = 'pk_test_51PHrmBHcGR1hqoMIXxE1Pir8otGi4UkJR303WP7iPmsG5579GHV0iMr02t8q2nFW6zU1H0W3bKa2HTvQ62IlwY7u006PmP52A8';
        $publishableKeyProd = 'pk_live_51N0QkII934CqzGCuHZkUc7pTktlExsX0YoHHWlVEBWdFSTeNWFyRM1eApDueRCIKymeQNDjyYraLMdVyzcvaY3mv003SReV1bd';

        echo json_encode(
        [
            'paymentIntent' => $paymentIntent->client_secret,
            'ephemeralKey' => $ephemeralKey->secret,
            'customer' => $customer->id,
            'bundleName' => $_POST['bundleName'],
            'userID' => auth()->user()->id, 
            'distributorID' => $_POST['distributorID'],
            'publishableKey' => $publishableKeyProd,
            'status' => 'success',
            'message' => 'Le prix du forfait est : ' . $bundlePrice . ' - ' . $_POST['bundlePrice']
        ]
        );
        http_response_code(200);

    }

    public function listenerPayment(Request $request){
        
        // webhook.php
        //
        // Use this sample code to handle webhook events in your integration.
        //
        // 1) Paste this code into a new file (webhook.php)
        //
        // 2) Install dependencies
        //   composer require stripe/stripe-php
        //
        // 3) Run the server on http://localhost:4242
        //   php -S localhost:4242

        //require '...vendor/autoload.php';

        // The library needs to be configured with your account's secret key.
        // Ensure the key is kept out of any version control system you might be using.
        
        // Dev
        //$stripe = new \Stripe\StripeClient('sk_test_51N0QkII934CqzGCuvU5O4U841kU9JJcOK6sM9zjxOOkllTpPUh4yhuBZBzllh3cJhkPUelucQOzvy8lKETRslLOS00HDjsQilx');



        // Prod
        //$stripe = new \Stripe\StripeClient('sk_test_51PHrmBHcGR1hqoMIeBaSrphxIv3LdyJiXnMFHkRSld6ZqMVvmcEkdaRY25BN2PkExkWCtq29WMgQ4X1anr069zsq000p9JZb4P');
        $stripe = new \Stripe\StripeClient('***********************************************************************************************************');

        // This is your Stripe CLI webhook secret for testing your endpoint locally.
        $endpoint_secret = 'whsec_nZwQenDKkoQYvTLIyZaJJ59klZY9KIDO';

        $payload = @file_get_contents('php://input');
        $sig_header = $_SERVER['HTTP_STRIPE_SIGNATURE'];
        $event = null;

        try {
        $event = \Stripe\Webhook::constructEvent(
            $payload, $sig_header, $endpoint_secret
        );
        } catch(\UnexpectedValueException $e) {
        // Invalid payload
        error_log("123");
        http_response_code(400);
        exit();
        } catch(\Stripe\Exception\SignatureVerificationException $e) {
        // Invalid signature
        error_log("345");
        http_response_code(400);
        exit();
        }

        // Handle the event
        switch ($event->type) {
        case 'payment_intent.amount_capturable_updated':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.canceled':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.created':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.partially_funded':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.payment_failed':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.processing':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.requires_action':
            $paymentIntent = $event->data->object;
            break;
        case 'payment_intent.succeeded':
            $paymentIntent = $event->data->object;

            // Si le forfait n'est pas un forfait V2, on retourne 200
            if (strpos($paymentIntent->metadata->bundleName, 'V2') === false) {
                http_response_code(200);
                exit();
            }else{

                // Récupérer l'ID de la charge
                $cardFingerprint = null;
                $chargeId = $paymentIntent->latest_charge;
                if ($chargeId) {
                    $charge = $stripe->charges->retrieve($chargeId);
                    if($charge->payment_method_details->card != null){
                        $cardFingerprint = $charge->payment_method_details->card->fingerprint;
                        Log::info("Empreinte digitale de la carte: " . $cardFingerprint);
                    }
                }

                // If user already have an esim
                $user_id = $paymentIntent->metadata->userID;

                // Get user credit by db
                $user_credit_referral = DB::select("SELECT credit, referral_by FROM users WHERE id = '" . $user_id . "'");
                $user_credit = $user_credit_referral[0]->credit;

                // Get referral by of user
                $user_referral_by = $user_credit_referral[0]->referral_by;

                $iccID = $this->eSimController->checkIfUserHaveEsim($user_id, $paymentIntent->metadata->bundleName);
                $bundleCreditUsed = $this->bundleController->getBundleCreditUsed($paymentIntent->metadata->bundleName, $user_credit);

                // Vérifier si c'est un premier achat
                $if_first_order = false;
                $user_order = DB::select("SELECT * FROM orders WHERE id_user = '" . $user_id . "'");
                if(count($user_order) == 0){
                    $if_first_order = true;
                }

                // If user already have an esim
                Log::info("bundleCreditUsed");
                Log::info($bundleCreditUsed);
                if( $iccID != false ){
                    Log::info("DEJA SIM");
                    $this->bundleController->addToInventory($iccID, $paymentIntent->metadata->bundleName, $paymentIntent->metadata->distributorID, $paymentIntent, $user_id, $bundleCreditUsed);
                }else{   
                    // Premier achat
                    Log::info("PAS DEJA SIM");
                    $this->bundleController->addFirstBundle($paymentIntent->metadata->bundleName, $paymentIntent->metadata->distributorID, $paymentIntent, $user_id, $bundleCreditUsed);
                }

                // Referral
                if(!empty($user_referral_by) && $if_first_order){

                    // Get id of user referral by
                    $user_referral_by_id = DB::select("SELECT id FROM users WHERE my_referral_code = '" . $user_referral_by . "'");
                    if(count($user_referral_by_id) > 0){
                        $user_referral_by_id = $user_referral_by_id[0]->id;
                    }else{
                        $user_referral_by_id = null;
                    }

                    // Verify if fingerprint of card was already used for referral
                    $fingerprints = DB::select("SELECT * FROM referrals WHERE stripe_card_fingerprint = '" . $cardFingerprint . "'");
                    if(count($fingerprints) == 0 && !empty($user_referral_by_id)){

                        // Mise à jour de la table referrals
                        DB::update("UPDATE referrals SET stripe_card_fingerprint = '" . $cardFingerprint . "', status = 'validated', amount = 3 WHERE new_user = '" . $user_id . "' AND referred_by = '" . $user_referral_by_id . "'");
                        
                        // Mise à jour du crédit de l'utilisateur référant
                        DB::update("UPDATE users SET credit = credit + 3 WHERE id = '" . $user_referral_by_id . "'");

                    }else{
                        // Mise à jour de la table referrals avec le statut card_already_used et on ne change pas le crédit de l'utilisateur
                        DB::update("UPDATE referrals SET stripe_card_fingerprint = '" . $cardFingerprint . "', status = 'card_already_used', amount = 0 WHERE new_user = '" . $user_id . "' AND referred_by = '" . $user_referral_by_id . "'");
                    }

                }
            }

            break;
        // ... handle other event types
        default:
            echo 'Received unknown event type ' . $event->type;
        }

        http_response_code(200);
    }

}
