<?php

namespace App\Http\Controllers;
use DB;
use Illuminate\Http\Request;
use App\Services\EsimGoService;
use App\Http\Controllers\BundleController;
use <PERSON><PERSON>\Lumen\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Mail;

class eSimController extends BaseController
{
    
    public function __construct(EsimGoService $eSimGoService)
    {
        //$this->bundleController = $BundleController;
        $this->eSimGoService = $eSimGoService;
        $this->middleware('auth:api', ['except' => ['compatibleDevices', 'updateEsims']]);
    }

    public function getAllEsim(){
        $active_zone = $this->getActiveZone();
        //$active_zone = "US";
        $allEsim = array();
        $user_id = auth()->user()->id;

        // Get all zones available for the user
        $esims_avaiables = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "'");
        $reload_available = DB::select("SELECT * FROM inventories WHERE user_id = '" . $user_id . "'");
        $zoneAvailable = array();
        foreach($esims_avaiables as $esim_avaiable){
            $zoneAvailable[] = $esim_avaiable->zone;
        }
        foreach($reload_available as $reload_available){
            $bundleInfo = explode("_", $reload_available->bundleName);
            $zone = $bundleInfo[3];
            $zone = $this->validateZone($zone);
            $zoneAvailable[] = $zone;
        }
        $zoneAvailable = array_unique($zoneAvailable);
        sort($zoneAvailable);
        
        // Filter inventories by zone
        $inventories = DB::select("SELECT * FROM inventories WHERE user_id = '" . $user_id . "'");
        $inventoriesFiltered = [];
        foreach($inventories as $inventory){
            $bundleInfo = explode("_", $inventory->bundleName);
            $zone = $bundleInfo[3];
            $zone = $this->validateZone($zone);
            if($zone == $active_zone){
                $inventoriesFiltered[] = $inventory;
            }
        }
        $inventoriesArray = $inventoriesFiltered;
        foreach($inventoriesArray as $key => $inventory){
            $bundleTable = getUpdatedBundleTable();
            $bundleInfo = DB::select("SELECT main_country, duration, dataAmount FROM " . $bundleTable . " WHERE name = '" . $inventory->bundleName . "'");
            $inventoriesArray[$key]->bundleInfo = $bundleInfo;
        }

        // Get eSIMs by zone
        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");
        if(count($esims) > 0){
        foreach($esims as $esim){

            $esimInfo = array("iccID" => $esim->iccid);

            // get esim info
            $esimBundle = $this->getBundlesInformationsNoAPI(1,$esim->iccid);
            $esimStatus = $this->getInstallStatus(1,$esim->iccid);

            // Inverser le array de bundle pour le mettre en ordre DESC
            $esimBundlesArray = json_decode($esimBundle);
            if(isset($esimBundlesArray->bundles)){
                $esimBundlesArray = array_reverse($esimBundlesArray->bundles);
            }else{
                $esimBundlesArray = [];
            }

            if(isset(json_decode($esimStatus)->firstInstalledDateTime)){
                $status = json_decode($esimStatus)->firstInstalledDateTime;
            }else{
                $status = "0";
            }

            // vérifier si un des bundles n'a pas d'assignement
            foreach($esimBundlesArray as $key => $bundle){
                if(empty($bundle->assignments)){
                    unset($esimBundlesArray[$key]);
                }
            }

            // Get country object
            $updatedCountry = getUpdatedCountryTable();
            $countryObject = DB::select("SELECT * FROM " . $updatedCountry . " WHERE iso = '" . $active_zone . "'");
            $countryObject = $countryObject[0];
            

            $allEsim[] = [
                'esimInfo' => $esimInfo,
                'bundles' => $esimBundlesArray,
                'status' => $status,
                'inventory' => $inventoriesArray,
                'zones' => $zoneAvailable,
                'country' => $active_zone,
                'countryObject' => $countryObject
                ];
            }
        }else{
            $allEsim[] = [
                'esimInfo' => [],
                'country' => $active_zone,
                'status' => "noEsim",
                'inventory' => $inventoriesArray,
                'zones' => $zoneAvailable
            ];
        }

        return $allEsim;
    }

    // MAIL TO SIMEO.CA
    public function sendQrCode(Request $request){
        $this->validate($request, [
            'distributorID' => 'required|integer'
        ]);

        $user_id = auth()->user()->id;
        $active_zone = $this->getActiveZone();
        $qrCode = $this->getQrCodeNoAPI($user_id, $request->input('distributorID'), $active_zone);
        $base64Image = $qrCode['url'];

        $email = auth()->user()->email;
        $this->distributorID = $request->input('distributorID');

        $data = [
            'u_qr' => encrypt_decrypt('encrypt', $base64Image),
            'u_email' => encrypt_decrypt('encrypt', $email),
        ];

        // SEND MAIL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "https://simeo.ca/mail/sendQRCode.php");
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        curl_close($ch);
        if($response == "SUCCESS"){
            return json_encode(['status' => 'success']);
        }else{
            return json_encode(['status' => 'error']);
        }

    }

    // OLD SEND MAIL VIA API
    public function sendQrCode2(Request $request){
        $this->validate($request, [
            'distributorID' => 'required|integer'
        ]);

        $user_id = auth()->user()->id;
        $active_zone = $this->getActiveZone();
        $qrCode = $this->getQrCodeNoAPI($user_id, $request->input('distributorID'), $active_zone);
        $base64Image = $qrCode['url'];
        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");

        $email = auth()->user()->email;
        $this->distributorID = $request->input('distributorID');
        
        //$type = pathinfo('.'.$esims[0]->image_url, PATHINFO_EXTENSION);
        //$base64Image = base64_encode(file_get_contents('.'.$esims[0]->image_url));

        $data = [
            'base64Image' => $base64Image
        ];

        // SEND MAIL
        $boundary = md5(uniqid(time()));
        $header[] = "MIME-Version: 1.0";
        $header[] = "Content-Type: Multipart/Mixed; Boundary=\"$boundary\"";
        $header[] = "Content-Transfer-Encoding: 7bit";
        $header[] = "From: Simeo <<EMAIL>>";
        $header[] = "Reply-To: Simeo <<EMAIL>>";
        $header[] = "X-Mailer: PHP/".phpversion();

        $msg[] = "";
        $msg[] = "--{$boundary}";
        $msg[] = "Content-Type: text/html; charset=UTF-8";
        $msg[] = "";
        $msg[] = "<p><img width='300' style='padding-bottom:10px;border-bottom: 1px solid #0F283C!important;' src='https://phpstack-1183811-4502079.cloudwaysapps.com/assets/img/simeoHeader.png' alt=\"Logo\"></p>";
        $msg[] = "<p style='color:#0F283C;font-size:14px'>Bonjour,</p>";
        $msg[] = "<p style='color:#0F283C;font-size:14px'>Vous trouverez le code QR pour l'activation en pièce jointe.</p>";
        $msg[] = "<p style='color:#0F283C;font-size:14px'>Cordialement,</p>";
        $msg[] = "<p style='color:#0F283C;font-size:14px'><strong>L'équipe Simeo</strong></p>";
        $msg[] = "";
        //HERE YOU MUST ATTACH THE IMAGE
        //===============================================
        $image = chunk_split($base64Image);
        $msg[] = "--{$boundary}";
        $msg[] = "Content-Type: image/png; name=\"Code - " . $active_zone . ".png\"";
        $msg[] = "Content-ID: <qrCode>";
        $msg[] = "Content-Description: Attachment";
        $msg[] = "Content-Transfer-Encoding: base64";
        $msg[] = "Content-Disposition: inline; filename=\"qrCode - " . $active_zone . ".png\"";
        $msg[] = "";
        $msg[] = $image;
        $msg[] = "";

        $msg[] = "--{$boundary}--";
        
        if(mail($email, "Activation - Votre code QR", implode("\r\n", $msg), implode("\r\n", $header))){
            return json_encode(['status' => 'success']);
        }else{
            return json_encode(['status' => 'error']);
        }

        

    }

    public function getQrCode(Request $request){
        $this->validate($request, [
            'distributorID' => 'required|integer'
        ]);

        $user_id = auth()->user()->id;
        $active_zone = $this->getActiveZone();
        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");
        if(!empty($esims[0]->apply_reference)){
            $this->reference = $esims[0]->apply_reference;
        }else{
            $order = DB::select("SELECT * FROM orders WHERE id_user = '" . $user_id . "' AND type = 'eSim' AND product_name LIKE '%- " . $active_zone . " -%'");
            $this->reference = $order[0]->order_reference;
        }
        
        $this->distributorID = $request->input('distributorID');

        // E-SIMGO
        if($this->distributorID == 1){


                $results = $this->eSimGoService->getQrCode($this->reference);
                $myfile = fopen($this->reference . ".zip", "w") or die("Unable to open file!");
                fwrite($myfile, $results);
                fclose($myfile);

                // Delete list.csv before extracting the zip
                if(file_exists('./qrCodes/list.csv')){
                    unlink('./qrCodes/list.csv');
                }

                $z = new \ZipArchive();
                $z->open($this->reference . ".zip");
                $z->extractTo('./qrCodes/');
                $z->close();

                // delete zip
                unlink($this->reference . ".zip");

                $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");
                
                DB::update("UPDATE esims SET image_url = '/qrCodes/" . $esims[0]->iccid . ".png' WHERE user_id = '" . $user_id . "'");

                $type = pathinfo('./qrCodes/' . $esims[0]->iccid . ".png", PATHINFO_EXTENSION);
                $base64Image = base64_encode(file_get_contents('./qrCodes/' . $esims[0]->iccid . ".png"));


                return [ 'url' => 'data:image/' . $type . ';base64,' . $base64Image ];
            
        }



        //return $results;

    }

    public function getQrCodeNoAPI($user_id, $distributorID, $active_zone){

        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");
        if(!empty($esims[0]->apply_reference)){
            $this->reference = $esims[0]->apply_reference;
        }else{
            $order = DB::select("SELECT * FROM orders WHERE id_user = '" . $user_id . "' AND type = 'eSim' AND product_name LIKE '%- " . $active_zone . " -%'");
            $this->reference = $order[0]->order_reference;
        }
        
        $this->distributorID = $distributorID;

        // E-SIMGO
        if($this->distributorID == 1){


                $results = $this->eSimGoService->getQrCode($this->reference);
                $myfile = fopen($this->reference . ".zip", "w") or die("Unable to open file!");
                fwrite($myfile, $results);
                fclose($myfile);

                // Delete list.csv before extracting the zip
                if(file_exists('./qrCodes/list.csv')){
                    unlink('./qrCodes/list.csv');
                }

                $z = new \ZipArchive();
                $z->open($this->reference . ".zip");
                $z->extractTo('./qrCodes/');
                $z->close();

                // delete zip
                unlink($this->reference . ".zip");

                $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");

                $type = pathinfo('./qrCodes/' . $esims[0]->iccid . ".png", PATHINFO_EXTENSION);
                $base64Image = base64_encode(file_get_contents('./qrCodes/' . $esims[0]->iccid . ".png"));


                return [ 'url' => $base64Image ];
            
        }



        //return $results;

    }

    public function getBundlesInformationsNoAPI($distributorID, $iccID){

        $this->iccID = $iccID;
        $this->distributorID = $distributorID;

        // E-SIMGO
        if($this->distributorID == 1){
            $results = $this->eSimGoService->getBundlesInformations($this->iccID);
        }

        $bundles = json_decode($results);
        if(isset($bundles->bundles)){
            foreach($bundles->bundles as $key => $result){
                $bundleInfo = $this->eSimGoService->getBundleInfo($result->name);
                if($bundleInfo != null){
                    $bundles->bundles[$key]->country = $bundleInfo->main_country;
                    $bundles->bundles[$key]->duration = $bundleInfo->duration;
                    $bundles->bundles[$key]->speed = $bundleInfo->speed;
                    $bundles->bundles[$key]->network = print_r($this->eSimGoService->getNetworksByCountry($bundleInfo->main_country), true);
                }else{
                    //mettre en array
                    $bundleName = explode("_", $result->name);
                    $bundles->bundles[$key]->country = $bundleName[3];
                    $bundles->bundles[$key]->duration = $bundleName[2][0];
                    $bundles->bundles[$key]->speed = "";
                    $bundles->bundles[$key]->network = print_r($this->eSimGoService->getNetworksByCountry($bundleName[3]), true);
                }
            }
        }else{
            $bundles = [];
        }

        $bundles = json_encode($bundles);
        return $bundles;

    }

    public function getBundlesInformations(Request $request){

        $this->validate($request, [
            'iccID' => 'required|string',
            'distributorID' => 'required|integer'
        ]);

        $this->iccID = $request->input('iccID');
        $this->distributorID = $request->input('distributorID');

        // E-SIMGO
        if($this->distributorID == 1){
            $results = $this->eSimGoService->getBundlesInformations($this->iccID);
        }

        return $results;

    }

    public function getEsimInfo($distributorID, $orderReference){

        $this->orderReference = $orderReference;
        $this->distributorID = $distributorID;

        // E-SIMGO
        if($this->distributorID == 1){
            $iccID = $this->eSimGoService->getEsimInfo($this->orderReference);
        }

        return $iccID;
    }

    public function getEsimInfoFromEsim(Request $request){
        $this->validate($request, [
            'distributorID' => 'required|integer'
        ]);

        $user_id = auth()->user()->id;
        $active_zone = $this->getActiveZone();
        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");
        $this->esim = $esims[0]->iccid;
        $this->distributorID = $request->input('distributorID');
        $iccID = $this->eSimGoService->getEsimInfoFromEsim($this->esim);
        return $iccID;
    }

    public function getInstallStatus($distributorID, $iccID){
            
            $this->iccID = $iccID;
    
            // E-SIMGO
            if($distributorID == 1){
                $status = $this->eSimGoService->getInstallStatus($this->iccID);
            }
    
            return $status;
    }

    // Check if a user already have an eSIM
    public function checkIfUserHaveEsim($userID, $bundleName){

        $this->userID = $userID;
        $this->bundleName = $bundleName;
        $bundleInfo = explode("_", $this->bundleName);
        $esimsCount = 0;
        if(isset($bundleInfo[3])){
            $mainCountry = $bundleInfo[3];
            $mainCountry = $this->validateZone($mainCountry);
            $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $this->userID . "' AND zone = '" . $mainCountry . "'");
            $esimsCount = count($esims);
        }

        if($esimsCount > 0){
            return $esims[0]->iccid;
        }else{
            return false;
        }

    }

    // add an eSIM to a user
    public function addEsimToUser($userID, $iccID, $orderID, $applyReference, $countryCode){

        $this->userID = $userID;
        $this->iccID = $iccID;

        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $this->userID . "' AND zone = '" . $countryCode . "'");
        $esimsCount = count($esims);

        if($esimsCount == 0){

            $label = "eSIM - " . $countryCode;

            DB::insert("INSERT INTO esims (user_id, order_id, iccid, created_at, apply_reference, zone, label, last_apply_date) 
                        VALUES ('" . $this->userID . "', 
                                '" . $orderID . "',
                                '" . $this->iccID . "',
                                '" . time() . "',
                                '" . $applyReference . "',
                                '" . $countryCode . "',
                                '" . $label . "',
                                '" . date("Ymd") . "'
                                )");
        }

    }

    public function sendConfirmationSMS(Request $request){
        $this->validate($request, [
            'distributorID' => 'required|integer'
        ]);

        $user_id = auth()->user()->id;
        $active_zone = $this->getActiveZone();  
        $esims = DB::select("SELECT * FROM esims WHERE user_id = '" . $user_id . "' AND zone = '" . $active_zone . "'");
        $this->distributorID = $request->input('distributorID');

        // E-SIMGO
        if($this->distributorID == 1){
            $results = $this->eSimGoService->sendConfirmationSMS($esims[0]->iccid);
        }

        return $esims[0]->iccid;
    }

    public function compatibleDevices(Request $request){
        $this->validate($request, [
        ]);

        $esims = DB::select("SELECT * FROM devices");

        return json_encode($esims);
    }

    public function getActiveZone($refresh = false){

        $user = auth()->user();

        $user_info = DB::select("SELECT * FROM users WHERE id = '" . $user->id . "'");
        $user_info = $user_info[0];
        $active_zone = $user_info->active_zone;
        $zone = "";
        if(empty($active_zone) || $refresh){
            $last_orders = DB::select("SELECT * FROM orders WHERE id_user = '" . $user->id . "' ORDER BY date DESC LIMIT 1");
            if(count($last_orders) > 0){
                $last_order = $last_orders[0];
                $product_name = $last_order->product_name;
                $bundleInfo = explode(" - ", $product_name);
                $zone = $bundleInfo[1];
                DB::update("UPDATE users SET active_zone = '" . $zone . "' WHERE id = '" . $user->id . "'");
                DB::update("UPDATE users SET valid_attempt = 0 WHERE id = '" . $user->id . "'");
            }else{
                $zone = "NoOrder";
            }
        }else{
            $zone = $active_zone;
        }

        return $zone;
    }

    public function changeActiveZone(Request $request){
        $this->validate($request, [
            'zone' => 'required|string'
        ]);

        $user_id = auth()->user()->id;
        DB::select("UPDATE users SET active_zone = '" . $request->input('zone') . "' WHERE id = '" . $user_id . "'");

        return json_encode(['status' => 'success']);
    }

    public function changeActiveZoneFromBundle($countryCode){
        
        
        $user_id = auth()->user();
        if($user_id != null){
            DB::select("UPDATE users SET active_zone = '" . $countryCode . "' WHERE id = '" . $user_id->id . "'");
        }

        return json_encode(['status' => 'success']);
    }

    public function updateEsims(){
        $esims = DB::select("SELECT * FROM esims");
        $esimsArray = [];
        foreach($esims as $esim){
            $esimBundle = $this->getBundlesInformationsNoAPI(1,$esim->iccid);
            $esimBundle = json_decode($esimBundle);
            $esimStatus = $this->getInstallStatus(1,$esim->iccid);

            if(isset($esimBundle->bundles) && count($esimBundle->bundles) > 0){
                $inactive = false;
                $country = $esimBundle->bundles[0]->country;
                $last_bundle = $esimBundle->bundles[0]->name;
                $last_apply_date = date("Ymd");
                $label = "eSIM - " . $country;
                DB::update("UPDATE esims SET zone = '" . $country . "', label = '" . $label . "', last_apply_date = '" . $last_apply_date . "' WHERE iccid = '" . $esim->iccid . "'");
            }else{
                $last_bundle = "none";
                $country = "none";
                $inactive = true;
            }
            $esimsArray[] = [
                'id' => $esim->id,
                'user_id' => $esim->user_id,
                'iccid' => $esim->iccid,
                'country' => $country,
                'last_bundle' => $last_bundle,
                'inactive' => $inactive,
            ];
        }
        return json_encode(['status' => 'success']);
    }

    public function validateZone($zone){
        if($zone == 'REUP'){
            $zone = 'Europe+';
        }
        if($zone == 'RME'){
            $zone = 'Middle East';
        }
        if($zone == 'RCA'){
            $zone = 'Caribbean';
        }
        if($zone == 'ROC'){
            $zone = 'Oceania';
        }
        if($zone == 'RAS'){
            $zone = 'Asia';
        }
        if($zone == 'RLA'){
            $zone = 'LATAM';
        }
        if($zone == 'RNA'){
            $zone = 'North America';
        }
        if($zone == 'RAF'){
            $zone = 'Africa';
        }
        if($zone == 'RGB'){
            $zone = 'Global';
        }
        return $zone;
    }
}
