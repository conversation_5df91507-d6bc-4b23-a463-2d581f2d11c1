<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use DB;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\BundleController;
use App\Http\Controllers\eSimController;
use  App\Models\User;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{


    public function __construct(BundleController $BundleController, eSimController $eSimController)
    {
        $this->middleware('auth:api', ['except' => ['login', 'logout', 'signUp', 'confirmAccount', 'forgotPassword', 'resetPassword']]);
        $this->bundleController = $BundleController;
        $this->eSimController = $eSimController;
    }
    /**
     * Get a JWT via given credentials.
     *
     * @param  Request  $request
     * @return Response
     */
    public function login(Request $request)
    {

        $this->validate($request, [
            'email' => 'required|string',
            'password' => 'required|string',
        ]);

        $email = $request->input('email');

        $credentials = $request->only(['email', 'password']);

        $expirationInMinutes = 60 * 24 * 14;
        Auth::factory()->setTTL($expirationInMinutes);
        if (! $token = Auth::attempt($credentials)) {
            return response()->json(['message' => 'Unauthorized'], 401);
        }

        $isValidateAccount = DB::select("SELECT * FROM users WHERE email = '" . $email . "' and (valid_account = 1 OR valid_account = 123456)" );
        if(count($isValidateAccount) == 0){
            
            $userAccount = DB::select("SELECT * FROM users WHERE email = '" . $email . "'" );
            $userAccount = $userAccount[0];

            // Envoi du courriel de confirmation
            $email =  encrypt_decrypt('encrypt', $email);
            $valid_number = encrypt_decrypt('encrypt', $userAccount->valid_account);

            $data = ['u_email' => $email, 'valid_number' => $valid_number];

            // SEND MAIL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://simeo.ca/mail/sendValidAccount.php");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);
            
            return response()->json(['message' => 'NeedVerification'], 401);
            
        }else{
            DB::select("UPDATE users SET valid_account = 1 WHERE email = '" . $email . "' and valid_account = 123456");
        }

        $this->eSimController->getActiveZone();

        return $this->respondWithToken($token);
    }

     /**
     * Get the authenticated User.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function me()
    {
        return response()->json(auth()->user());
    }

    /**
     * Log the user out (Invalidate the token).
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout()
    {
        auth()->logout();

        return response()->json(['message' => 'Successfully logged out']);
    }

    /**
     * Refresh a token.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function refresh()
    {
        return $this->respondWithToken(auth()->refresh());
    }

    /**
     * Get the token array structure.
     *
     * @param  string $token
     *
     * @return \Illuminate\Http\JsonResponse
     */
    protected function respondWithToken($token)
    {

        // get esim
        $esim = DB::select("SELECT * FROM esims WHERE user_id = '" . auth()->user()->id . "'" );
        $esimSelected = "";
        if(count($esim) > 0){
            $esimSelected = $esim[0]->iccid;
        }

        $toReturn = [
            'esim' => $esimSelected,
            'access_token' => $token,
            'token_type' => 'bearer',
            'user' => auth()->user(),
            'expires_in' => auth()->factory()->getTTL()
        ];

        return response()->json($toReturn);
    }

    public function signUp(Request $request)
    {

        $blacklist = ['ogrmux.com', 'naipeq.com', 'pdoax.com', 'kunsum.com'];
        $whitelist = ['assnat.qc.ca'];

        $this->validate($request, [
            'name' => 'string',
            'email' => 'required|string',
            'password' => 'required|string',
            'password_confirmation' => 'required|string',
            'referral_code' => 'string'
        ]);

        // Gestion erreur
        if($request->input('password') != $request->input('password_confirmation')){
            return [
                'status' => 'error',
                'msg' => 'Les mots de passe ne correspondent pas.'
            ];
        }

        // minimum 8 characters, at least one letter and one number
        if(strlen($request->input('password')) < 8 || 
           !preg_match('/[a-zA-Z]/', $request->input('password')) || 
           !preg_match('/[0-9]/', $request->input('password'))){
            return [
                'status' => 'error',
                'msg' => 'Le mot de passe doit contenir au moins 8 caractères, une lettre et un nombre.'
            ];
        }

        if (!filter_var($request->input('email'), FILTER_VALIDATE_EMAIL)) {
            return [
                'status' => 'error',
                'msg' => 'L\'adresse courriel n\'est pas valide.'
            ];
        }

        foreach ($blacklist as $domain) {
            if (strpos($request->input('email'), $domain) !== false) {
                return [
                    'status' => 'error',
                    'msg' => 'L\'adresse courriel n\'est pas valide.'
                ];
            }
        }

        // is valid email
        if(!filter_var($request->input('email'), FILTER_VALIDATE_EMAIL)){
            return [
                'status' => 'error',
                'msg' => 'L\'adresse courriel n\'est pas valide.'
            ];
        }

        // if .comm or mistake like that
        if(preg_match('/\.(comm|caa)\b/', $request->input('email'))){
            return [
                'status' => 'error',
                'msg' => 'L\'adresse courriel n\'est pas valide.'
            ];
        }

        $name = $request->input('name');
        $email = $request->input('email');
        $password = Hash::make($request->input('password'));
        $referral_code = $request->input('referral_code');
        $credit = 0;
        $invalid = false;
        $invalid_msg = '';
        $found_code = false;

        if(!empty($referral_code)){

            

            // PromoCode à l'inscription
            
            if(!$found_code){
                $promoCode = DB::select("SELECT * FROM promoCodes");
                foreach($promoCode as $code){
                        
                    if(strtolower($code->code_promo) == strtolower($referral_code) and $code->type == 'Inscription'){
                        $date_debut = $code->date_debut;
                        $date_fin = $code->date_fin;
                        $rabais = $code->rabais;
                        $date_debut = strtotime($date_debut);
                        $date_fin = strtotime($date_fin);
                        $now = time();
                        $bypass_date = false;
                        if(empty($date_debut) or empty($date_fin)){
                            $bypass_date = true;
                        }
                        if($bypass_date or ($now >= $date_debut and $now <= $date_fin)){
                            $invalid = false;
                            $credit = $rabais;
                            $referral_by = null;
                            $found_code = true;
                            DB::select("UPDATE promoCodes SET nombre_utilisations = nombre_utilisations + 1 WHERE code_promo = '" . $code->code_promo . "'");
                        }else{
                            $invalid = true;
                            $invalid_msg = 'Code promo invalide ou expiré.';
                        }
                    }else{
                        $invalid = true;
                        $invalid_msg = 'Code ami ou promotion invalide ou expiré.';
                    }
                }
            }

            if(!$found_code){
                $referral_by = DB::select("SELECT * FROM users WHERE my_referral_code = '" . $referral_code . "'" );
                if(count($referral_by) == 0){
                    $invalid = true;
                    $invalid_msg = 'Code de parrainage invalide.';
                }else{
                    $referral_by = $referral_by[0]->id;
                    $credit = 3;
                    $found_code = true;
                    $invalid = false;
                }
            }

            if($invalid){
                return [
                    'status' => 'error',
                    'msg' => $invalid_msg
                ];
            }
        }else{
            $referral_by = null;
        }

        $signup = DB::select("SELECT * FROM users WHERE email = '" . $email . "'" );
        if(count($signup) == 0){

            // Enregistrement en base de données
            $isWhitelisted = false;
            foreach ($whitelist as $domain) {
                if (strpos($email, $domain) !== false) {
                    $isWhitelisted = true;
                }
            }
            
            $isWhitelisted = true;
            if($isWhitelisted){
                $code_confirmation = "123456";
            }else{
                $code_confirmation = random_int(100000, 999999);
            }

            DB::select("INSERT INTO users (name, email, password, pass_token, created_at, updated_at, stripe_customer_id, valid_account, referral_by, credit, valid_attempt)
                                             VALUES ('".$name."',
                                                     '".$email."',
                                                     '".$password."',
                                                     '',
                                                     ". time() . ",
                                                     ". time() . ",
                                                     '',
                                                     " . $code_confirmation . ",
                                                     '" . $referral_code . "',
                                                     '" . $credit . "',
                                                     0)");

            // Récupérer l'ID de l'utilisateur nouvellement créé
            $newUser = DB::select("SELECT id FROM users WHERE email = '" . $email . "'" );
            $newUserId = $newUser[0]->id;

            if(!empty($referral_by)){
                $newReferral = DB::select("INSERT INTO referrals (new_user, referred_by) VALUES (" . $newUserId . ", " . $referral_by . ")");
            }

            // Générer le code de parrainage
            $my_referral_code = "SIMEO" . $newUserId;

            // Mettre à jour l'utilisateur avec le code de parrainage
            DB::select("UPDATE users SET my_referral_code = '" . $my_referral_code . "' WHERE id = " . $newUserId);

            // Envoi du courriel de confirmation
            $email =  encrypt_decrypt('encrypt', $email);
            $valid_number = encrypt_decrypt('encrypt', $code_confirmation);

            $data = ['u_email' => $email, 'valid_number' => $valid_number];

            // SEND MAIL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://simeo.ca/mail/sendValidAccount.php");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);

            if($isWhitelisted){
                $msg = "bypass";
            }else{
                $msg = "Registration Successful. Please Login to proceed";
            }
            return [
                'status' => 'success',
                'msg' => $msg
            ];
        }else{
            // Courriel deja choisi
            return [
                'status' => 'error',
                'msg' => "L'adresse courriel est déjà utilisée."
            ];
        }

    }

    public function confirmAccount(Request $request){

        $this->validate($request, [
            'email' => 'required|string',
            'code' => 'required|string'
        ]);

        $email = $request->input('email');
        $code = $request->input('code');

        // maximum of 3 attempts
        $maxAttempts = 3;
        $user = DB::select("SELECT * FROM users WHERE email = '" . $email . "'");
        if($user[0]->valid_attempt >= $maxAttempts){
            return [
                'status' => 'error',
                'msg' => 'Trop de tentatives. Veuillez contacter le service client à l\'adresse <EMAIL>.'
            ];
        }

        $signup = DB::select("SELECT * FROM users WHERE email = '" . $email . "' and valid_account = " . intval($code) );
        if(count($signup) == 0){
            DB::select("UPDATE users SET valid_attempt = valid_attempt + 1 WHERE email = '" . $email . "'");
            return [
                'status' => 'error',
                'msg' => 'Code de confirmation invalide.'
            ];
        }else{
            DB::select("UPDATE users SET valid_account = 1 WHERE email = '" . $email . "' and valid_account = " . intval($code) );
            return [
                'status' => 'success',
                'msg' => 'Compte confirmé.'
            ];
        }

    }

    public function changePassword(Request $request){
            
            $this->validate($request, [
                'oldPassword' => 'required|string',
                'newPassword' => 'required|string',
                'newPasswordConfirmation' => 'required|string'
            ]);
    
            $oldPassword = $request->input('oldPassword');
            $newPassword = $request->input('newPassword');
            $newPasswordConfirmation = $request->input('newPasswordConfirmation');
    
            if($newPassword != $newPasswordConfirmation){
                return [
                    'status' => 'error',
                    'msg' => 'Les mots de passe ne correspondent pas.'
                ];
            }
    
            $user = auth()->user();
            if(Hash::check($oldPassword, $user->password)){
                $user->password = Hash::make($newPassword);
                DB::select("UPDATE users SET password = '" . $user->password . "', updated_at = " . time() . " WHERE id = '" . $user->id . "'");
                return [
                    'status' => 'success',
                    'msg' => 'Mot de passe changé.'
                ];
            }else{
                return [
                    'status' => 'error',
                    'msg' => 'Ancien mot de passe incorrect.'
                ];
            }
    }

    public function deleteAccount(){
        
        $user = auth()->user();

        // Supprimer esim 
        $esim = DB::select("DELETE FROM esims WHERE user_id = '" . $user->id . "'" );

        // Supprimer le user
        DB::select('DELETE FROM users WHERE id = ' . $user->id . '');

        return [
            'status' => 'success',
            'msg' => 'Compte supprimé.'
        ];

    }

    public function forgotPassword(Request $request){
        $this->validate($request, [
            'email' => 'required|string'
        ]);

        $email = $request->input('email');

        if (!filter_var($request->input('email'), FILTER_VALIDATE_EMAIL)) {
            return [
                'status' => 'error',
                'msg' => 'L\'adresse courriel n\'est pas valide.'
            ];
        }

        $signup = DB::select("SELECT * FROM users WHERE email = '" . $email . "'" );
        if(count($signup) == 0){
            return [
                'status' => 'error',
                'msg' => 'Adresse courriel non trouvé.'
            ];
        }else{
            $userAccount = $signup[0];
            $code_confirmation = random_int(100000, 999999);

            DB::select("UPDATE users SET pass_token = " . $code_confirmation . " WHERE email = '" . $email . "'" );

            // Envoi du courriel de confirmation
            $email =  encrypt_decrypt('encrypt', $email);
            $valid_number = encrypt_decrypt('encrypt', $code_confirmation);

            $data = ['u_email' => $email, 'reset_pass_code' => $valid_number];

            // SEND MAIL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://simeo.ca/mail/sendResetPassCode.php");
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $response = curl_exec($ch);
            curl_close($ch);

            if($response != "SUCCESS"){
                return [
                    'status' => 'error',
                    'msg' => 'Temporairement indisponible'
                ];
            }else{
                return [
                    'status' => 'success',
                    'msg' => 'Courriel envoyé.'
                ];
            }
        }
    }

    public function resetPassword(Request $request){
        $this->validate($request, [
            'email' => 'required|string',
            'code' => 'required|string',
            'newPassword' => 'required|string',
            'newPasswordConfirmation' => 'required|string'
        ]);

        $email = $request->input('email');
        $code = $request->input('code');
        $newPassword = $request->input('newPassword');
        $newPasswordConfirmation = $request->input('newPasswordConfirmation');

        if($code == "" || $newPassword == "" || $newPasswordConfirmation == ""){
            return [
                'status' => 'error',
                'msg' => 'Tous les champs sont requis.'
            ];
        }

        if($code == 0 || $code == ""){
            return [
                'status' => 'error',
                'msg' => 'Code de confirmation invalide.'
            ];
        }

        if($newPassword != $newPasswordConfirmation){
            return [
                'status' => 'error',
                'msg' => 'Les mots de passe ne correspondent pas.'
            ];
        }

        $signup = DB::select("SELECT * FROM users WHERE email = '" . $email . "' and pass_token = " . intval($code) );
        if(count($signup) == 0){
            return [
                'status' => 'error',
                'msg' => 'Code de confirmation invalide.'
            ];
        }else{
            DB::select("UPDATE users SET password = '" . Hash::make($newPassword) . "', pass_token = '0', updated_at = " . time() . " WHERE email = '" . $email . "' and pass_token = " . intval($code) );
            return [
                'status' => 'success',
                'msg' => 'Mot de passe changé.'
            ];
        }
    }
}
