<?php

namespace App\Console;

use <PERSON>luminate\Console\Scheduling\Schedule;
use <PERSON><PERSON>\Lumen\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        Commands\SyncCountries::class,
        Commands\SyncBundles::class,
        Commands\ListOldEsims::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        //$schedule->command('command:syncCountries')->everyFiveMinutes();
        //$schedule->command('command:syncBundles')->everyFiveMinutes();

        // Exécute la commande de listing des eSIM obsolètes chaque nuit à 02:00
        $schedule->command('command:listOldEsims')->dailyAt('02:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
